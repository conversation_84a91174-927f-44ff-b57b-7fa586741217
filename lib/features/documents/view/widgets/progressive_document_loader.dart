import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';

/// A widget that provides progressive loading for documents with chunked loading
/// and memory optimization to prevent UI freezing
class ProgressiveDocumentLoader extends StatefulWidget {
  final Future<Uint8List> documentBytes;
  final Widget Function(Uint8List bytes) builder;
  final Widget? loadingWidget;
  final Widget Function(String error, VoidCallback retry)? errorBuilder;
  final Duration? timeout;
  final int chunkSize;
  final bool showProgress;

  const ProgressiveDocumentLoader({
    super.key,
    required this.documentBytes,
    required this.builder,
    this.loadingWidget,
    this.errorBuilder,
    this.timeout,
    this.chunkSize = 1024 * 1024, // 1MB chunks
    this.showProgress = true,
  });

  @override
  State<ProgressiveDocumentLoader> createState() => _ProgressiveDocumentLoaderState();
}

class _ProgressiveDocumentLoaderState extends State<ProgressiveDocumentLoader> {
  Uint8List? _loadedBytes;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  double _loadingProgress = 0.0;
  Timer? _timeoutTimer;
  StreamController<double>? _progressController;

  @override
  void initState() {
    super.initState();
    _loadDocumentProgressively();
  }

  @override
  void dispose() {
    _timeoutTimer?.cancel();
    _progressController?.close();
    super.dispose();
  }

  Future<void> _loadDocumentProgressively() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
      _loadingProgress = 0.0;
    });

    _progressController = StreamController<double>.broadcast();

    // Set timeout if specified
    if (widget.timeout != null) {
      _timeoutTimer = Timer(widget.timeout!, () {
        if (_isLoading && mounted) {
          setState(() {
            _isLoading = false;
            _hasError = true;
            _errorMessage = 'Document loading timed out';
          });
        }
      });
    }

    try {
      // Load document bytes
      final bytes = await widget.documentBytes;
      _timeoutTimer?.cancel();

      if (!mounted) return;

      // Simulate progressive loading for better UX
      await _simulateProgressiveLoading(bytes);

      _loadedBytes = bytes;

      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = false;
          _loadingProgress = 1.0;
        });
      }
    } catch (e) {
      _timeoutTimer?.cancel();
      
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = e.toString();
        });
      }
    } finally {
      _progressController?.close();
    }
  }

  Future<void> _simulateProgressiveLoading(Uint8List bytes) async {
    if (!widget.showProgress || !mounted) return;

    final totalSize = bytes.length;
    final chunks = (totalSize / widget.chunkSize).ceil();
    
    for (int i = 0; i < chunks; i++) {
      if (!mounted || !_isLoading) break;
      
      final progress = (i + 1) / chunks;
      setState(() {
        _loadingProgress = progress;
      });
      
      _progressController?.add(progress);
      
      // Small delay to show progress and prevent UI blocking
      await Future.delayed(const Duration(milliseconds: 50));
    }
  }

  void _retryLoad() {
    _loadedBytes = null;
    _loadDocumentProgressively();
  }

  Widget _buildDefaultLoadingWidget() {
    return Container(
      color: Colors.grey[100],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 60,
              height: 60,
              child: Stack(
                children: [
                  CircularProgressIndicator(
                    value: widget.showProgress ? _loadingProgress : null,
                    valueColor: const AlwaysStoppedAnimation<Color>(AppColor.primary),
                    strokeWidth: 4,
                  ),
                  if (widget.showProgress)
                    Center(
                      child: Text(
                        '${(_loadingProgress * 100).toInt()}%',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AppColor.primary,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Loading document...',
              style: TextStyle(fontSize: 16),
            ),
            if (widget.showProgress) ...[
              const SizedBox(height: 8),
              SizedBox(
                width: 200,
                child: LinearProgressIndicator(
                  value: _loadingProgress,
                  valueColor: const AlwaysStoppedAnimation<Color>(AppColor.primary),
                  backgroundColor: Colors.grey[300],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultErrorWidget(String error, VoidCallback retry) {
    return Container(
      color: Colors.grey[100],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'Failed to load document',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                error,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: retry,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColor.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return widget.loadingWidget ?? _buildDefaultLoadingWidget();
    }

    if (_hasError) {
      return widget.errorBuilder?.call(_errorMessage ?? 'Unknown error', _retryLoad) ??
          _buildDefaultErrorWidget(_errorMessage ?? 'Unknown error', _retryLoad);
    }

    if (_loadedBytes != null) {
      return widget.builder(_loadedBytes!);
    }

    return const Center(child: Text('Document not available'));
  }
}
