import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/services/document_cache_service.dart';

/// A widget that optimizes document loading with caching, error handling, and memory management
class OptimizedDocumentLoader extends StatefulWidget {
  final Future<Uint8List> documentBytes;
  final Widget Function(Uint8List bytes) builder;
  final Widget? loadingWidget;
  final Widget Function(String error, VoidCallback retry)? errorBuilder;
  final Duration? timeout;
  final bool enableCache;
  final String? cacheKey;

  const OptimizedDocumentLoader({
    super.key,
    required this.documentBytes,
    required this.builder,
    this.loadingWidget,
    this.errorBuilder,
    this.timeout,
    this.enableCache = true,
    this.cacheKey,
  });

  @override
  State<OptimizedDocumentLoader> createState() =>
      _OptimizedDocumentLoaderState();
}

class _OptimizedDocumentLoaderState extends State<OptimizedDocumentLoader> {
  Uint8List? _cachedBytes;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  Timer? _timeoutTimer;
  late final DocumentCacheService _cacheService;

  @override
  void initState() {
    super.initState();
    _cacheService = DocumentCacheService();
    _loadDocument();
  }

  @override
  void dispose() {
    _timeoutTimer?.cancel();
    super.dispose();
  }

  String _getCacheKey() {
    return widget.cacheKey ?? widget.documentBytes.hashCode.toString();
  }

  Future<void> _loadDocument() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
    });

    // Check cache first if enabled
    if (widget.enableCache) {
      final cacheKey = _getCacheKey();
      final cachedBytes = await _cacheService.getDocument(cacheKey);
      if (cachedBytes != null) {
        _cachedBytes = cachedBytes;
        if (mounted) {
          setState(() {
            _isLoading = false;
            _hasError = false;
          });
        }
        return;
      }
    }

    // Set timeout if specified
    if (widget.timeout != null) {
      _timeoutTimer = Timer(widget.timeout!, () {
        if (mounted && _isLoading) {
          setState(() {
            _isLoading = false;
            _hasError = true;
            _errorMessage = 'Document loading timed out';
          });
        }
      });
    }

    try {
      // Load document
      final bytes = await widget.documentBytes;
      _timeoutTimer?.cancel();

      if (!mounted) return;

      _cachedBytes = bytes;

      // Cache the document if enabled
      if (widget.enableCache) {
        final cacheKey = _getCacheKey();
        await _cacheService.cacheDocument(cacheKey, bytes);
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = false;
        });
      }
    } catch (e) {
      _timeoutTimer?.cancel();

      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = e.toString();
        });
      }
    }
  }

  void _retryLoad() {
    _cachedBytes = null;
    _loadDocument();
  }

  Widget _buildDefaultLoadingWidget() {
    return Container(
      color: Colors.grey[100],
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColor.primary),
            ),
            SizedBox(height: 16),
            Text(
              'Loading document...',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultErrorWidget(String error, VoidCallback retry) {
    return Container(
      color: Colors.grey[100],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'Failed to load document',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                error,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: retry,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColor.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return widget.loadingWidget ?? _buildDefaultLoadingWidget();
    }

    if (_hasError) {
      return widget.errorBuilder
              ?.call(_errorMessage ?? 'Unknown error', _retryLoad) ??
          _buildDefaultErrorWidget(
              _errorMessage ?? 'Unknown error', _retryLoad);
    }

    if (_cachedBytes != null) {
      return widget.builder(_cachedBytes!);
    }

    return const Center(child: Text('Document not available'));
  }
}
