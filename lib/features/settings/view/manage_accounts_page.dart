import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/database/hive/hive_extensions.dart';
import 'package:paperless_mobile/features/login/cubit/authentication_cubit.dart';
import 'package:paperless_mobile/features/settings/view/widgets/global_settings_builder.dart';
import 'package:paperless_mobile/features/users/view/widgets/user_account_list_tile.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:paperless_mobile/routing/routes/add_account_route.dart';
import 'package:provider/provider.dart';

class ManageAccountsPage extends StatelessWidget {
  const ManageAccountsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GlobalSettingsBuilder(
      builder: (context, globalSettings) {
        return ValueListenableBuilder(
          valueListenable: Hive.localUserAccountBox.listenable(),
          builder: (context, box, _) {
            if (globalSettings.loggedInUserId == null) {
              return const SizedBox.shrink();
            }
            final userIds = box.keys.toList().cast<String>();
            final otherAccounts = userIds
                .whereNot((element) => element == globalSettings.loggedInUserId)
                .toList();
            return SimpleDialog(
              // contentPadding: const EdgeInsets.all(8),
              titlePadding: const EdgeInsets.only(left: 10, right: 10, top: 4),
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.transparent,
              title: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const SizedBox(
                    width: 48,
                  ),
                  Center(
                      child: Text(
                    S.of(context)!.accounts,
                    style: const TextStyle(
                        fontSize: 16, fontWeight: FontWeight.w600),
                  )),
                  IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close))
                ],
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Card(
                    color: AppColor.blue_E5EDF6,
                    child: UserAccountListTile(
                      account: box.get(globalSettings.loggedInUserId!)!,
                      trailing: IconButton(
                        onPressed: () async {
                          Navigator.of(context).pop();
                          await context
                              .read<AuthenticationCubit>()
                              .logout(true);
                        },
                        icon: SvgPicture.asset('assets/svgs/logout.svg'),
                      ),
                    ),
                  ),
                ),
                Column(
                  children: [
                    for (int index = 0; index < otherAccounts.length; index++)
                      UserAccountListTile(
                        account: box.get(otherAccounts[index])!,
                        trailing: PopupMenuButton(
                          icon: const Icon(Icons.more_vert),
                          itemBuilder: (context) {
                            return [
                              PopupMenuItem(
                                value: 0,
                                child: ListTile(
                                  title: Text(S.of(context)!.switchAccount),
                                  leading:
                                      const Icon(Icons.switch_account_rounded),
                                ),
                              ),
                              PopupMenuItem(
                                value: 1,
                                child: ListTile(
                                  title: Text(S.of(context)!.remove),
                                  leading: const Icon(
                                    Icons.person_remove,
                                    color: Colors.red,
                                  ),
                                ),
                              )
                            ];
                          },
                          onSelected: (value) async {
                            if (value == 0) {
                              // Switch
                              _onSwitchAccount(
                                context,
                                globalSettings.loggedInUserId!,
                                otherAccounts[index],
                              );
                            } else if (value == 1) {
                              await context
                                  .read<AuthenticationCubit>()
                                  .removeAccount(otherAccounts[index]);
                            }
                          },
                        ),
                      ),
                  ],
                ),
                const Divider(),
                ListTile(
                  title: Text(
                    S.of(context)!.addAccount,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  leading: const Icon(
                    Icons.person_add,
                    color: AppColor.primary,
                  ),
                  onTap: () {
                    _onAddAccount(context, globalSettings.loggedInUserId!);
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _onAddAccount(BuildContext context, String currentUser) async {
    Navigator.of(context).pop();
    const AddAccountRoute().push<String>(context);
  }

  void _onSwitchAccount(
    BuildContext context,
    String currentUser,
    String newUser,
  ) async {
    if (currentUser == newUser) return;

    Navigator.of(context).pop();
    await context.read<AuthenticationCubit>().switchAccount(newUser);
  }
}
