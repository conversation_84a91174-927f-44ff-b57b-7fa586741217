import 'dart:async';
import 'dart:typed_data';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// Service for caching documents with memory and disk cache management
class DocumentCacheService {
  static final DocumentCacheService _instance = DocumentCacheService._internal();
  factory DocumentCacheService() => _instance;
  DocumentCacheService._internal();

  // Memory cache with size limit
  final Map<String, Uint8List> _memoryCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  
  // Configuration
  static const int maxMemoryCacheSize = 5; // Maximum number of documents in memory
  static const int maxMemoryCacheSizeMB = 50; // Maximum memory cache size in MB
  static const Duration cacheExpiration = Duration(hours: 24);
  
  Directory? _cacheDirectory;
  int _currentMemoryUsage = 0;

  /// Initialize the cache service
  Future<void> initialize() async {
    try {
      final tempDir = await getTemporaryDirectory();
      _cacheDirectory = Directory('${tempDir.path}/document_cache');
      
      if (!await _cacheDirectory!.exists()) {
        await _cacheDirectory!.create(recursive: true);
      }
      
      // Clean up expired cache files on startup
      await _cleanupExpiredCache();
    } catch (e) {
      debugPrint('Failed to initialize document cache: $e');
    }
  }

  /// Generate cache key from document identifier
  String _generateCacheKey(String identifier) {
    final bytes = utf8.encode(identifier);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Get document from cache (memory first, then disk)
  Future<Uint8List?> getDocument(String identifier) async {
    final cacheKey = _generateCacheKey(identifier);
    
    // Check memory cache first
    if (_memoryCache.containsKey(cacheKey)) {
      final timestamp = _cacheTimestamps[cacheKey];
      if (timestamp != null && DateTime.now().difference(timestamp) < cacheExpiration) {
        debugPrint('Document loaded from memory cache: $identifier');
        return _memoryCache[cacheKey];
      } else {
        // Remove expired entry
        _removeFromMemoryCache(cacheKey);
      }
    }
    
    // Check disk cache
    if (_cacheDirectory != null) {
      final file = File('${_cacheDirectory!.path}/$cacheKey.cache');
      if (await file.exists()) {
        final stat = await file.stat();
        if (DateTime.now().difference(stat.modified) < cacheExpiration) {
          try {
            final bytes = await file.readAsBytes();
            // Add to memory cache for faster access next time
            await _addToMemoryCache(cacheKey, bytes);
            debugPrint('Document loaded from disk cache: $identifier');
            return bytes;
          } catch (e) {
            debugPrint('Failed to read cached document: $e');
            // Delete corrupted cache file
            await file.delete().catchError((_) {});
          }
        } else {
          // Delete expired cache file
          await file.delete().catchError((_) {});
        }
      }
    }
    
    return null;
  }

  /// Cache document in both memory and disk
  Future<void> cacheDocument(String identifier, Uint8List bytes) async {
    final cacheKey = _generateCacheKey(identifier);
    
    // Add to memory cache
    await _addToMemoryCache(cacheKey, bytes);
    
    // Add to disk cache
    if (_cacheDirectory != null) {
      try {
        final file = File('${_cacheDirectory!.path}/$cacheKey.cache');
        await file.writeAsBytes(bytes);
        debugPrint('Document cached to disk: $identifier');
      } catch (e) {
        debugPrint('Failed to cache document to disk: $e');
      }
    }
  }

  /// Add document to memory cache with size management
  Future<void> _addToMemoryCache(String cacheKey, Uint8List bytes) async {
    final sizeInMB = bytes.length / (1024 * 1024);
    
    // Check if adding this document would exceed memory limits
    while ((_memoryCache.length >= maxMemoryCacheSize || 
            _currentMemoryUsage + sizeInMB > maxMemoryCacheSizeMB) &&
           _memoryCache.isNotEmpty) {
      await _removeOldestFromMemoryCache();
    }
    
    _memoryCache[cacheKey] = bytes;
    _cacheTimestamps[cacheKey] = DateTime.now();
    _currentMemoryUsage += sizeInMB.ceil();
    
    debugPrint('Document added to memory cache. Size: ${sizeInMB.toStringAsFixed(2)}MB, Total: ${_currentMemoryUsage}MB');
  }

  /// Remove document from memory cache
  void _removeFromMemoryCache(String cacheKey) {
    final bytes = _memoryCache.remove(cacheKey);
    _cacheTimestamps.remove(cacheKey);
    
    if (bytes != null) {
      final sizeInMB = bytes.length / (1024 * 1024);
      _currentMemoryUsage -= sizeInMB.ceil();
      _currentMemoryUsage = _currentMemoryUsage.clamp(0, double.infinity).toInt();
    }
  }

  /// Remove oldest document from memory cache
  Future<void> _removeOldestFromMemoryCache() async {
    if (_cacheTimestamps.isEmpty) return;
    
    final oldestEntry = _cacheTimestamps.entries
        .reduce((a, b) => a.value.isBefore(b.value) ? a : b);
    
    _removeFromMemoryCache(oldestEntry.key);
  }

  /// Clean up expired cache files
  Future<void> _cleanupExpiredCache() async {
    if (_cacheDirectory == null || !await _cacheDirectory!.exists()) return;
    
    try {
      final files = await _cacheDirectory!.list().toList();
      final now = DateTime.now();
      
      for (final entity in files) {
        if (entity is File && entity.path.endsWith('.cache')) {
          final stat = await entity.stat();
          if (now.difference(stat.modified) > cacheExpiration) {
            await entity.delete();
            debugPrint('Deleted expired cache file: ${entity.path}');
          }
        }
      }
    } catch (e) {
      debugPrint('Failed to cleanup expired cache: $e');
    }
  }

  /// Clear all cache (memory and disk)
  Future<void> clearAllCache() async {
    // Clear memory cache
    _memoryCache.clear();
    _cacheTimestamps.clear();
    _currentMemoryUsage = 0;
    
    // Clear disk cache
    if (_cacheDirectory != null && await _cacheDirectory!.exists()) {
      try {
        await _cacheDirectory!.delete(recursive: true);
        await _cacheDirectory!.create(recursive: true);
        debugPrint('All document cache cleared');
      } catch (e) {
        debugPrint('Failed to clear disk cache: $e');
      }
    }
  }

  /// Clear memory cache only
  void clearMemoryCache() {
    _memoryCache.clear();
    _cacheTimestamps.clear();
    _currentMemoryUsage = 0;
    debugPrint('Memory cache cleared');
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'memoryCache': {
        'count': _memoryCache.length,
        'sizeMB': _currentMemoryUsage,
        'maxSizeMB': maxMemoryCacheSizeMB,
      },
      'diskCache': {
        'directory': _cacheDirectory?.path,
        'exists': _cacheDirectory?.existsSync() ?? false,
      },
    };
  }

  /// Check if document is cached
  Future<bool> isDocumentCached(String identifier) async {
    final cacheKey = _generateCacheKey(identifier);
    
    // Check memory cache
    if (_memoryCache.containsKey(cacheKey)) {
      final timestamp = _cacheTimestamps[cacheKey];
      if (timestamp != null && DateTime.now().difference(timestamp) < cacheExpiration) {
        return true;
      }
    }
    
    // Check disk cache
    if (_cacheDirectory != null) {
      final file = File('${_cacheDirectory!.path}/$cacheKey.cache');
      if (await file.exists()) {
        final stat = await file.stat();
        return DateTime.now().difference(stat.modified) < cacheExpiration;
      }
    }
    
    return false;
  }
}
